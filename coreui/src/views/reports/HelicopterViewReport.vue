<template>
  <c-col col="12" lg="12">
    <filter-data @getHelicopterViewReport="filter"/>

    <!-- Loading State -->
    <c-card v-if="isLoading">
      <c-card-body class="text-center">
        <c-spinner size="lg" class="mb-3"/>
        <h5>Generating helicopter view report...</h5>
        <p class="text-muted">This may take a few moments for large datasets.</p>
      </c-card-body>
    </c-card>

    <!-- Summary Statistics Section -->
    <c-card v-if="summaryStats && !isLoading">
      <c-card-header>
        <h4 class="text-center">
          Helicopter View Report Summary
          <br/>
          <small class="text-muted">
            From: {{ filterData.from_date }} to: {{ filterData.to_date }}
            | Lines: {{ filterData.line_ids ? filterData.line_ids.length : 0 }}
          </small>
        </h4>
      </c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_records }}</h3>
                <p class="mb-0">Total Accounts</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.lines_covered }}</h3>
                <p class="mb-0">Lines Covered</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-info text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.total_visits }}</h3>
                <p class="mb-0">Total Visits</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h3>{{ formatUnits(summaryStats.total_sales) }}</h3>
                <p class="mb-0">Total Sales</p>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-secondary text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.average_coverage }}%</h3>
                <p class="mb-0">Average Coverage</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-dark text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.performance_score }}%</h3>
                <p class="mb-0">Performance Score</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="card bg-danger text-white">
              <div class="card-body text-center">
                <h3>{{ summaryStats.growth_rate }}%</h3>
                <p class="mb-0">Growth Rate</p>
              </div>
            </div>
          </div>
        </div>

      </c-card-body>
    </c-card>

    <!-- Trends Analysis Section -->
    <c-card v-if="trendsData && filterData.include_trends && !isLoading">
      <c-card-header>
        <h4 class="text-center">
          <c-icon name="cil-chart-line" class="mr-2"/>
          Trend Analysis
          <br/>
          <small class="text-muted">
            Performance trends over the selected period
          </small>
        </h4>
      </c-card-header>
      <c-card-body>
        <!-- Trend Summary Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-gradient-primary text-white">
              <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                  <c-icon :name="getTrendIcon(trendsData.sales_trend)" size="2xl" class="mr-2"/>
                  <div>
                    <h3>{{ trendsData.sales_trend }}%</h3>
                    <p class="mb-0">Sales Trend</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-gradient-success text-white">
              <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                  <c-icon :name="getTrendIcon(trendsData.visits_trend)" size="2xl" class="mr-2"/>
                  <div>
                    <h3>{{ trendsData.visits_trend }}%</h3>
                    <p class="mb-0">Visits Trend</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-gradient-info text-white">
              <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                  <c-icon :name="getTrendIcon(trendsData.coverage_trend)" size="2xl" class="mr-2"/>
                  <div>
                    <h3>{{ trendsData.coverage_trend }}%</h3>
                    <p class="mb-0">Coverage Trend</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="card bg-gradient-warning text-white">
              <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center">
                  <c-icon :name="getTrendIcon(trendsData.performance_trend)" size="2xl" class="mr-2"/>
                  <div>
                    <h3>{{ trendsData.performance_trend }}%</h3>
                    <p class="mb-0">Performance Trend</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Trend Details Table -->
        <div class="row">
          <div class="col-12">
            <h5 class="mb-3">
              <c-icon name="cil-list" class="mr-2"/>
              Detailed Trend Analysis
            </h5>
            <div class="table-responsive">
              <table class="table table-hover table-striped">
                <thead class="thead-dark">
                  <tr>
                    <th>Metric</th>
                    <th>Current Period</th>
                    <th>Previous Period</th>
                    <th>Change</th>
                    <th>Trend Direction</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="trend in trendsData.detailed_trends" :key="trend.metric">
                    <td class="font-weight-bold">{{ trend.metric_name }}</td>
                    <td>{{ formatTrendValue(trend.current_value, trend.metric) }}</td>
                    <td>{{ formatTrendValue(trend.previous_value, trend.metric) }}</td>
                    <td :class="getTrendChangeClass(trend.change_percentage)">
                      {{ trend.change_percentage > 0 ? '+' : '' }}{{ trend.change_percentage }}%
                    </td>
                    <td>
                      <c-icon
                        :name="getTrendIcon(trend.change_percentage)"
                        :class="getTrendIconClass(trend.change_percentage)"
                        size="lg"
                      />
                    </td>
                    <td>
                      <c-badge
                        :color="getTrendStatusColor(trend.change_percentage)"
                        class="px-2 py-1"
                      >
                        {{ getTrendStatus(trend.change_percentage) }}
                      </c-badge>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Trend Insights -->
        <div class="row mt-4" v-if="trendsData.insights && trendsData.insights.length > 0">
          <div class="col-12">
            <h5 class="mb-3">
              <c-icon name="cil-lightbulb" class="mr-2"/>
              Key Insights
            </h5>
            <div class="row">
              <div class="col-lg-6 col-md-12" v-for="(insight, index) in trendsData.insights" :key="index">
                <div class="alert" :class="getInsightAlertClass(insight.type)">
                  <div class="d-flex align-items-start">
                    <c-icon
                      :name="getInsightIcon(insight.type)"
                      size="lg"
                      class="mr-3 mt-1"
                    />
                    <div>
                      <h6 class="alert-heading mb-1">{{ insight.title }}</h6>
                      <p class="mb-0">{{ insight.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <!-- Data Table Section -->
    <c-card v-if="reportData.length > 0 && !isLoading">
      <c-card-header>
        <div class="d-flex justify-content-between align-items-center">
          <h4>Helicopter View Analysis Results</h4>
          <div class="d-flex align-items-center">
            <span class="text-muted mr-2">
              {{ reportData.length }} records found
            </span>
          </div>
        </div>
      </c-card-header>
      <c-card-body>
        <!-- Vue2DataTable for Performance -->
        <Vue2DataTable
          :columns="tableColumns"
          :data-source="reportData"
          :show-pagination="true"
          :show-search="true"
          :show-total-bar="true"
          :row-height="60"
          :column-width="200"
          :virtual-scroll-threshold="1000"
          search-placeholder="Search helicopter view data..."
          @search="handleSearch"
        >
          <!-- Custom slot for sales amount -->
          <template #total_sales="{ value }">
            <span class="text-success font-weight-bold">
              {{ formatUnits(value) }}
            </span>
          </template>

          <!-- Custom slot for coverage percentage -->
          <template #coverage_percentage="{ value }">
            <span :class="getCoverageClass(value)">
              {{ value }}%
            </span>
          </template>

          <!-- Custom slot for performance score -->
          <template #performance_score="{ value }">
            <span :class="getPerformanceClass(value)">
              {{ value }}%
            </span>
          </template>

          <!-- Custom slot for growth rate -->
          <template #growth_rate="{ value }">
            <span :class="getGrowthClass(value)">
              {{ value > 0 ? '+' : '' }}{{ value }}%
            </span>
          </template>

          <!-- Custom slot for date -->
          <template #date="{ value }">
            <span class="text-muted">
              {{ formatDate(value) }}
            </span>
          </template>
        </Vue2DataTable>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="exportFields"
          :data="exportData"
          :name="reportName"
        />
      </c-card-footer>
    </c-card>

    <!-- No Data Message -->
    <c-card v-if="!isLoading && reportData.length === 0 && hasSearched">
      <c-card-body class="text-center">
        <c-icon name="cil-chart-line" size="3xl" class="text-muted mb-3"/>
        <h4 class="text-muted">No Data Found</h4>
        <p class="text-muted">
          No helicopter view data found for the selected criteria.
          Try adjusting your filters or date range.
        </p>
      </c-card-body>
    </c-card>
  </c-col>
</template>

<script>
import moment from 'moment';
import FilterData from '../../components/reports/HelicopterView/filterData.vue';
import Vue2DataTable from '../../components/common/Vue2DataTable/components/core/Vue2DataTable.vue';
import Download from '../../components/download-reports/download.vue';
import LargeDatasetHandler from "../../mixins/LargeDatasetHandler.js";

export default {
  name: 'HelicopterViewReport',
  components: {
    FilterData,
    Vue2DataTable,
    Download
  },
  mixins: [LargeDatasetHandler],
  data() {
    return {
      // Loading states
      isLoading: false,
      hasSearched: false,

      // Filter data
      filterData: {},

      // Report data
      reportData: [],
      summaryStats: null,
      trendsData: null,

      // Table configuration
      tableColumns: [
        {
          key: 'line_name',
          label: 'Line',
          sortable: true,
          searchable: true,
          width: 150,
          align: 'center'
        },
        {
          key: 'total_accounts',
          label: 'Accounts',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        },
        {
          key: 'total_visit_coverage',
          label: 'Visits',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        },
        {
          key: 'total_call_rate',
          label: 'Call Rates',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        },
        {
          key: 'total_frequency',
          label: 'Frequency',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        },
        {
          key: 'total_sales_quantities',
          label: 'Sales',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        },
        {
          key: 'total_targets_units',
          label: 'Targets',
          sortable: true,
          searchable: false,
          width: 100,
          align: 'center'
        }

      ]
    };
  },
  computed: {
    exportFields() {
      return this.tableColumns.map(col => ({
        key: col.key,
        label: col.title
      }));
    },
    exportData() {
      return this.reportData.map(item => {
        const exportItem = {};
        this.exportFields.forEach(field => {
          if (field.key === 'date') {
            exportItem[field.key] = this.formatDate(item[field.key]);
          } else if (field.key === 'total_sales') {
            exportItem[field.key] = this.formatUnits(item[field.key]);
          } else if (field.key.includes('percentage') || field.key.includes('score') || field.key.includes('rate')) {
            exportItem[field.key] = `${item[field.key]}%`;
          } else {
            exportItem[field.key] = item[field.key];
          }
        });
        return exportItem;
      });
    },
    reportName() {
      const fromDate = this.filterData.from_date || moment().startOf('month').format('YYYY-MM-DD');
      const toDate = this.filterData.to_date || moment().endOf('month').format('YYYY-MM-DD');
      return `helicopter_view_report_${fromDate}_to_${toDate}`;
    },
  },
  methods: {
    async filter(data) {
      this.filterData = data.filters;
      this.isLoading = true;
      this.hasSearched = true;

      try {
        const { res, error } = await this.tryCatch(
          axios.post('/api/helicopter-view-report', this.filterData)
        );

        if (error) {
          this.showErrorMessage(error);
          return;
        }

        // Handle the response based on user's manual changes
        this.reportData = res.data.data.metrics || [];
        this.summaryStats = res.data.data.summaryStats || null;
        this.trendsData = res.data.data.trends || null;


        if (this.reportData.length === 0) {
          this.flash('No data found for the selected criteria.', 'warning');
        } else {
          this.flash(`Successfully loaded ${this.reportData.length} records.`, 'success');
        }
      } catch (err) {
        console.error('Error generating helicopter view report:', err);
        this.showErrorMessage(err);
      } finally {
        this.isLoading = false;
      }
    },

    handleSearch(searchTerm) {
      // Handle search functionality if needed
      console.log('Search term:', searchTerm);
    },

    // Formatting methods
    formatDate(date) {
      if (!date) return '';
      return moment(date).format('DD/MM/YYYY');
    },

    formatUnits(amount, unit = '') {
      if (!amount && amount !== 0) return '';
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);

      return unit ? `${formatted} ${unit}` : formatted;
    },

    // CSS class methods for styling
    getCoverageClass(value) {
      if (value >= 80) return 'text-success font-weight-bold';
      if (value >= 60) return 'text-warning font-weight-bold';
      return 'text-danger font-weight-bold';
    },

    getPerformanceClass(value) {
      if (value >= 90) return 'text-success font-weight-bold';
      if (value >= 70) return 'text-info font-weight-bold';
      if (value >= 50) return 'text-warning font-weight-bold';
      return 'text-danger font-weight-bold';
    },

    getGrowthClass(value) {
      if (value > 0) return 'text-success font-weight-bold';
      if (value === 0) return 'text-muted font-weight-bold';
      return 'text-danger font-weight-bold';
    },

    // Trend analysis methods
    getTrendIcon(value) {
      if (value > 0) return 'cil-arrow-top';
      if (value < 0) return 'cil-arrow-bottom';
      return 'cil-minus';
    },

    getTrendIconClass(value) {
      if (value > 0) return 'text-success';
      if (value < 0) return 'text-danger';
      return 'text-muted';
    },

    getTrendChangeClass(value) {
      if (value > 0) return 'text-success font-weight-bold';
      if (value < 0) return 'text-danger font-weight-bold';
      return 'text-muted font-weight-bold';
    },

    getTrendStatus(value) {
      if (value > 10) return 'Excellent';
      if (value > 5) return 'Good';
      if (value > 0) return 'Positive';
      if (value === 0) return 'Stable';
      if (value > -5) return 'Declining';
      if (value > -10) return 'Poor';
      return 'Critical';
    },

    getTrendStatusColor(value) {
      if (value > 10) return 'success';
      if (value > 5) return 'info';
      if (value > 0) return 'primary';
      if (value === 0) return 'secondary';
      if (value > -5) return 'warning';
      if (value > -10) return 'danger';
      return 'dark';
    },

    formatTrendValue(value, metric) {
      if (!value && value !== 0) return 'N/A';

      switch (metric) {
        case 'sales':
          return this.formatUnits(value);
        case 'coverage':
        case 'performance':
          return `${value}%`;
        case 'visits':
          return value.toLocaleString();
        default:
          return value;
      }
    },

    getInsightIcon(type) {
      switch (type) {
        case 'positive':
          return 'cil-thumb-up';
        case 'negative':
          return 'cil-thumb-down';
        case 'warning':
          return 'cil-warning';
        case 'info':
          return 'cil-info';
        default:
          return 'cil-lightbulb';
      }
    },

    getInsightAlertClass(type) {
      switch (type) {
        case 'positive':
          return 'alert-success';
        case 'negative':
          return 'alert-danger';
        case 'warning':
          return 'alert-warning';
        case 'info':
          return 'alert-info';
        default:
          return 'alert-light';
      }
    },

    // Download methods
    print() {
      window.print();
    },

    download() {
      // Excel download handled by Download component
    },

    createPDF() {
      // PDF creation handled by Download component
    },

    downloadCsv() {
      // CSV download handled by Download component
    }
  },
};
</script>

<style scoped>
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/* Custom card hover effects */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* Loading spinner customization */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Table hover effects */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

/* Button group spacing */
.btn + .btn {
  margin-left: 0.5rem;
}

/* Form validation styles */
.is-invalid {
  border-color: #dc3545;
}

.is-valid {
  border-color: #28a745;
}

/* Custom alert styles */
.alert {
  border-radius: 0.375rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
  }

  .gap-2 > * {
    margin-bottom: 0.5rem;
  }

  .gap-2 > *:last-child {
    margin-bottom: 0;
  }
}

/* Trends section styles */
.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-gradient-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%) !important;
}

.trend-card {
  transition: all 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
}

.trend-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.trend-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-heading {
  font-weight: 600;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.thead-dark th {
  background-color: #343a40;
  border-color: #454d55;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

/* Trend animation */
@keyframes trendPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.trend-positive {
  animation: trendPulse 2s ease-in-out infinite;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    border: none !important;
    box-shadow: none !important;
  }

  .table {
    font-size: 12px;
  }

  .bg-gradient-primary,
  .bg-gradient-success,
  .bg-gradient-info,
  .bg-gradient-warning {
    background: #f8f9fa !important;
    color: #000 !important;
  }
}
</style>
